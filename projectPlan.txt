## Adding a new feature to the smart screen control frontend project.

# Add event and ads report page
1. create a report page for event and ads report With modern, attractive and interactive design
  ## i created a new page for the report event-ads-report.component.ts and event-ads-report.component.html
  and use the component to create the report page and all routing is done so no need to change that.

 ## i created a backend Api for the report


GET http://localhost:3030/v1/api/reports/posting?startDate=2017,12,1&endDate=2017,12,30
## create a service and call the api from the service also the permission is needed so add the authorization header to the service.

# also this is optional startDate=2017,12,1&endDate=2017,12,30 also it is ethiopian date

so we need to date ethiopian date selector for start and end date look at ads.component.ts how the ethiopian start date and end date picker implemented also no need to install any package for the ethiopian date picker

Also the date picker is sperated Year, month, day dropdowns so it will be easy to implement
## note
The Start Date and endDate is optional

## Note
Please make the UI  modern and attractive also make sure if the image is not uploaded then it will show the placeholder

here is the api response
[
    {
        "postedBy": "<PERSON>",
        "type": "Ad",
        "title": "What is Lorem Ipsum?",
        "description": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
        "imageUrl": "uploads\\1756733524530-istockphoto-1344512181-612x612.jpg",
        "createdAt": "2025-09-01T16:32:04.550Z"
    },
    {
        "postedBy": "Abel Tesfaye",
        "type": "Ad",
        "title": "Why do we use it?",
        "description": "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).",
        "imageUrl": null,
        "createdAt": "2025-08-26T11:09:09.060Z"
    },
    {
        "postedBy": "Abel Tesfaye",
        "type": "Event",
        "title": "Adwa",
        "description": "Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of \"de Finibus Bonorum et Malorum\" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, \"Lorem ipsum dolor sit amet..\", comes from a line in section 1.10.32.",
        "imageUrl": null,
        "createdAt": "2025-08-18T14:22:02.090Z"
    },
    {
        "postedBy": "Abel Tesfaye",
        "type": "Event",
        "title": "Why do we use it?",
        "description": "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).",
        "imageUrl": null,
        "createdAt": "2025-08-18T12:50:29.267Z"
    },
    {
        "postedBy": "Abel Tesfaye",
        "type": "Event",
        "title": "ETHIOPIAN DAY SILVER SPRING, MD 2025 የኢትዮጵያ ቀን",
        "description": "የኢትዮጵያ ቀን ፌስቲቫል የኢትዮጵያን ባህል፣ወግ እና ማህበረሰብ የሚያከብር፣ሙዚቃ፣ዳንስ፣ምግብ እና የእጅ ጥበብ ስራዎችን የያዘ የባህል ዝግጅት ነው። በሲልቨር ስፕሪንግ ሜሪላንድ ዓመታዊው የኢትዮጵያውያን ቀን ፌስቲቫል እ.ኤ.አ. ኦገስት 31 ቀን 2025 የኢትዮጵያውያን ቅርሶችን በድምቀት ይከበራል።የኢትዮጵያ ቀን ፌስቲቫል የኢትዮጵያን ባህል፣ወግ እና ማህበረሰብ የሚያከብር፣ሙዚቃ፣ዳንስ፣ምግብ እና የእጅ ጥበብ ስራዎችን የያዘ የባህል ዝግጅት ነው። በሲልቨር ስፕሪንግ ሜሪላንድ ዓመታዊው የኢትዮጵያውያን ቀን ፌስቲቫል እ.ኤ.አ. ኦገስት 31 ቀን 2025 የኢትዮጵያውያን ቅርሶችን በድምቀት ይከበራል።",
        "imageUrl": "uploads\\1755508849185-á¨á¢áµá®áµá«-áá-Ethiopian-day-2025-silver-spring.jpg",
        "createdAt": "2025-08-18T12:20:49.217Z"
    },
    {
        "postedBy": "Abel Tesfaye",
        "type": "Event",
        "title": "Where can I get some?",
        "description": "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.",
        "imageUrl": "uploads\\1754560285317-wp5055273-dark-car-wallpapers.jpg",
        "createdAt": "2025-08-07T12:51:25.357Z"
    },
    {
        "postedBy": "Abel Tesfaye",
        "type": "Event",
        "title": "What is Lorem Ipsum?",
        "description": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
        "imageUrl": "uploads\\1754559687247-SILLY BERNARD THE BEAR â¡â¥â¡â¥â¡â¥â¡â¥.jpeg",
        "createdAt": "2025-08-07T12:41:03.640Z"
    }
]

