.screen-control-container {
  padding: 10px;
  position: relative;

  &.loading-container {
    .action-header,
    .ultra-modern-tabs {
      opacity: 0.7;
      pointer-events: none;
    }
  }
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.5);

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
    position: relative;
    padding-left: 15px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 5px;
      height: 24px;
      background: linear-gradient(to bottom, #1890ff, #096dd9);
      border-radius: 3px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }

  .add-button {
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    color: white;

    &.ad-btn {
      background: linear-gradient(135deg, #52c41a, #389e0d);
      box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);

      &:hover {
        background: linear-gradient(135deg, #389e0d, #237804);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
      }
    }

    &.event-btn {
      background: linear-gradient(135deg, #1890ff, #096dd9);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

      &:hover {
        background: linear-gradient(135deg, #096dd9, #0050b3);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
      }
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(82, 196, 26, 0.5);
      background: linear-gradient(135deg, #73d13d, #52c41a);
    }
  }
}

// Ultra Modern Custom Tabs
.ultra-modern-tabs {
  margin-bottom: 32px;

  .tabs-container {
    display: flex;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    gap: 4px;

    .tab-item {
      flex: 1;

      &.loading {
        .tab-icon {
          nz-spin {
            color: #1890ff;
          }
        }
      }
      position: relative;
      cursor: pointer;
      border-radius: 16px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;

      .tab-content {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 20px 24px;
        position: relative;
        z-index: 2;

        .tab-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          transition: all 0.4s ease;
          background: rgba(255, 255, 255, 0.5);
          color: #64748b;
        }

        .tab-text {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .tab-title {
            font-size: 16px;
            font-weight: 600;
            color: #334155;
            transition: all 0.3s ease;
          }

          .tab-subtitle {
            font-size: 12px;
            color: #94a3b8;
            font-weight: 500;
            transition: all 0.3s ease;
          }
        }
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 3px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 2px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(-50%);
      }

      &:hover:not(.active) {
        background: rgba(255, 255, 255, 0.6);
        transform: translateY(-2px);

        .tab-icon {
          background: rgba(255, 255, 255, 0.8);
          color: #475569;
          transform: scale(1.05);
        }

        .tab-title {
          color: #1e293b;
        }

        .tab-subtitle {
          color: #64748b;
        }
      }

      &.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        transform: translateY(-4px);

        .tab-icon {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          transform: scale(1.1);
          box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
        }

        .tab-title {
          color: white;
        }

        .tab-subtitle {
          color: rgba(255, 255, 255, 0.8);
        }

        .tab-indicator {
          width: 40px;
          background: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .tabs-container {
      flex-direction: column;
      gap: 8px;

      .tab-item {
        .tab-content {
          padding: 16px 20px;

          .tab-icon {
            width: 40px;
            height: 40px;
            font-size: 18px;
          }

          .tab-text {
            .tab-title {
              font-size: 14px;
            }

            .tab-subtitle {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// Ultra Modern Grid with Animation
.ads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 32px;
  padding: 16px;
  width: 100%;
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 12px;
  }

  @media (min-width: 1400px) {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 40px;
  }
}

// Fade in animation
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Ultra Modern Card
.modern-ad-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(25px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 400px;

  // Subtle gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
  }

  &:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.12),
      0 0 0 1px rgba(102, 126, 234, 0.1),
      0 1px 0 rgba(255, 255, 255, 0.6) inset;
    border-color: rgba(102, 126, 234, 0.2);
  }

  &.event-card {
    background: linear-gradient(145deg, rgba(230, 247, 255, 0.95), rgba(186, 230, 253, 0.85));

    &:hover {
      box-shadow:
        0 25px 50px rgba(24, 144, 255, 0.15),
        0 0 0 1px rgba(24, 144, 255, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.6) inset;
      border-color: rgba(24, 144, 255, 0.2);
    }
  }



  // Status Badge - Positioned at top-right
  .status-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    i {
      font-size: 14px;
    }

    &.active {
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.95), rgba(22, 163, 74, 0.95));
      color: white;
      box-shadow: 0 6px 24px rgba(34, 197, 94, 0.4);

      &:hover {
        transform: scale(1.05) translateY(-2px);
        box-shadow: 0 8px 32px rgba(34, 197, 94, 0.5);
      }
    }

    &.expired {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.95), rgba(220, 38, 38, 0.95));
      color: white;
      box-shadow: 0 6px 24px rgba(239, 68, 68, 0.4);

      &:hover {
        transform: scale(1.05) translateY(-2px);
        box-shadow: 0 8px 32px rgba(239, 68, 68, 0.5);
      }
    }
  }

  // Enhanced Card Header
  .card-header {
    padding: 28px 28px 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .ad-icon {
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 22px;
      box-shadow:
        0 8px 24px rgba(102, 126, 234, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      flex-shrink: 0;

      &::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        height: 50%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
        border-radius: 16px 16px 50% 50%;
      }

      &:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow:
          0 12px 32px rgba(102, 126, 234, 0.35),
          0 0 0 1px rgba(255, 255, 255, 0.2) inset;
      }
    }

    .ad-info {
      flex: 1;
      min-width: 0;
      padding-right: 120px; // Space for status badge

      .ad-title {
        font-size: 19px;
        font-weight: 700;
        color: #1a202c;
        margin: 0 0 12px 0;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        letter-spacing: -0.4px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }

      .ad-meta {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #4a5568;
        font-size: 13px;
        font-weight: 600;
        background: rgba(255, 255, 255, 0.4);
        padding: 8px 16px;
        border-radius: 25px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        width: fit-content;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        i {
          font-size: 14px;
          color: #667eea;
        }
      }
    }
  }

  // Image Preview
  .image-preview {
    position: relative;
    height: 180px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);

    .ad-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.4s ease;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      color: white;
      font-size: 24px;
    }

    &:hover {
      .ad-image {
        transform: scale(1.1);
      }

      .image-overlay {
        opacity: 1;
      }
    }
  }

  // No Image Placeholder
  .no-image-placeholder {
    height: 180px;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    gap: 8px;

    i {
      font-size: 32px;
      opacity: 0.6;
    }

    span {
      font-size: 13px;
      font-weight: 500;
    }
  }

  // Event Info Placeholder
  .event-info-placeholder {
    height: 180px;
    background: linear-gradient(135deg, #e6f7ff, #bae7ff);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #1890ff;
    gap: 8px;

    i {
      font-size: 32px;
      opacity: 0.8;
    }

    span {
      font-size: 13px;
      font-weight: 500;
    }
  }

  // Description
  .ad-description {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .description-text {
      color: #4a5568;
      font-size: 14px;
      line-height: 1.6;
      margin: 0 0 12px 0;
      flex: 1;
      font-weight: 400;

      .ellipsis {
        color: #a0aec0;
        font-weight: 500;
      }
    }

    .read-more-btn {
      background: none;
      border: none;
      color: #667eea;
      cursor: pointer;
      font-size: 13px;
      font-weight: 600;
      padding: 8px 0;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.3s ease;
      align-self: flex-start;
      border-radius: 6px;

      &:hover {
        color: #4c51bf;
        background: rgba(102, 126, 234, 0.05);
        padding: 8px 12px;
        margin: 0 -12px;
      }

      i {
        font-size: 12px;
        transition: transform 0.3s ease;
      }

      &:hover i {
        transform: translateY(1px);
      }
    }
  }
}

.no-data-message {
  text-align: center;
  padding: 60px 20px;
  color: #666;

  .empty-state {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 16px;
    padding: 40px 20px;
    max-width: 500px;
    margin: 0 auto;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

    .empty-icon {
      font-size: 60px;
      color: #bfbfbf;
      margin-bottom: 20px;
      display: block;
    }

    h3 {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
    }

    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 25px;
    }

    .empty-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .empty-action {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
      border: none;
      font-size: 16px;

      i {
        font-size: 18px;
      }

      &:hover {
        transform: translateY(-2px);
      }

      &.btn-primary {
        background: linear-gradient(135deg, #52c41a, #389e0d);
        color: white;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);

        &:hover {
          box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
        }
      }

      &.btn-secondary {
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

        &:hover {
          box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
        }
      }
    }
  }
}

// Modern Drawer Styles
.modern-drawer-container {
  ::ng-deep .ant-drawer-body {
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
      pointer-events: none;
    }
  }

  ::ng-deep .ant-drawer-header {
    display: none;
  }
}

// Modern Header
.modern-drawer-header {
  position: relative;
  z-index: 2;
  padding: 32px 32px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  }

  .header-text {
    h3 {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      letter-spacing: -0.5px;
    }

    p {
      margin: 0;
      color: #718096;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .close-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #718096;
    font-size: 18px;

    &:hover {
      background: rgba(255, 255, 255, 1);
      color: #e53e3e;
      transform: scale(1.05);
    }
  }
}

// Modern Content
.modern-drawer-content {
  position: relative;
  z-index: 2;
  padding: 24px 32px;
  height: calc(100vh - 200px);
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

// Ultra Modern Form
.ultra-modern-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .card-header {
    padding: 24px 28px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    gap: 16px;

    .card-icon {
      width: 44px;
      height: 44px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

    .card-title {
      h4 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        letter-spacing: -0.3px;
      }

      span {
        color: #718096;
        font-size: 13px;
        font-weight: 500;
      }
    }
  }

  .form-fields {
    padding: 28px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

// Modern Form Fields
.field-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .modern-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 4px;

    i {
      font-size: 16px;
      color: #667eea;
    }

    .required {
      color: #e53e3e;
      font-weight: 700;
    }
  }

  .input-wrapper {
    position: relative;

    .modern-input, .modern-textarea {
      width: 100%;
      padding: 16px 20px;
      border: 2px solid rgba(226, 232, 240, 0.8);
      border-radius: 16px;
      font-size: 15px;
      font-weight: 500;
      color: #2d3748;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      outline: none;
      resize: none;

      &::placeholder {
        color: #a0aec0;
        font-weight: 400;
      }

      &:focus {
        border-color: #667eea;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
      }

      &:hover:not(:focus) {
        border-color: rgba(102, 126, 234, 0.4);
      }
    }

    .modern-textarea {
      min-height: 120px;
      line-height: 1.6;
    }

    .modern-select {
      width: 100%;
      padding: 16px 20px;
      border: 2px solid rgba(226, 232, 240, 0.8);
      border-radius: 16px;
      font-size: 15px;
      font-weight: 500;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: #4a5568;
      appearance: none;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 16px center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 48px;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
      }

      &:hover:not(:focus) {
        border-color: rgba(102, 126, 234, 0.4);
      }

      option {
        padding: 12px;
        background: white;
        color: #4a5568;
      }
    }

    ::ng-deep .modern-datepicker {
      width: 100%;

      .ant-picker {
        width: 100%;
        padding: 16px 20px;
        border: 2px solid rgba(226, 232, 240, 0.8);
        border-radius: 16px;
        font-size: 15px;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          border-color: rgba(102, 126, 234, 0.4);
        }

        &.ant-picker-focused {
          border-color: #667eea;
          box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }
      }
    }

    .input-border {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }
  }

  .field-error {
    color: #e53e3e;
    font-size: 13px;
    font-weight: 500;
    margin-top: 4px;
    padding-left: 4px;
    display: flex;
    align-items: center;
    gap: 6px;

    &::before {
      content: '⚠';
      font-size: 12px;
    }
  }
}

// Upload Section
.upload-section {
  padding: 28px;

  .existing-image {
    margin-bottom: 24px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .existing-image-preview {
      position: relative;
      display: inline-block;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      margin-bottom: 12px;

      .current-image {
        width: 120px;
        height: 120px;
        object-fit: cover;
        display: block;
      }

      .existing-image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        color: white;
        padding: 8px 12px;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
      }
    }

    .existing-image-text,
    .no-existing-image-text {
      margin: 0;
      font-size: 13px;
      color: #718096;
      line-height: 1.5;
    }

    .existing-image-text {
      color: #4a5568;
    }

    .no-existing-image-text {
      color: #a0aec0;
      font-style: italic;
    }
  }

  .upload-area {
    border: 2px dashed rgba(226, 232, 240, 0.8);
    border-radius: 20px;
    background: rgba(248, 250, 252, 0.5);
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      border-color: rgba(102, 126, 234, 0.4);
      background: rgba(102, 126, 234, 0.02);
    }

    &.has-files {
      border-color: #667eea;
      background: rgba(102, 126, 234, 0.05);
    }

    ::ng-deep .modern-upload {
      .ant-upload.ant-upload-select-picture-card {
        width: 100%;
        height: auto;
        min-height: 160px;
        border: none;
        border-radius: 20px;
        background: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .ant-upload-list-picture-card {
        .ant-upload-list-item {
          border-radius: 16px;
          overflow: hidden;
          border: 2px solid rgba(255, 255, 255, 0.8);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .upload-content {
      padding: 40px 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .upload-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 28px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        margin-bottom: 8px;
      }

      .upload-text {
        .upload-title {
          font-size: 16px;
          font-weight: 600;
          color: #4a5568;
          margin: 0 0 4px 0;
        }

        .upload-subtitle {
          font-size: 13px;
          color: #718096;
          margin: 0;
          font-weight: 500;
        }
      }
    }
  }
}

// Modern Footer
.modern-drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3;
  padding: 24px 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }

  .modern-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 16px;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }

    &.btn-secondary {
      background: rgba(226, 232, 240, 0.8);
      color: #4a5568;
      border: 2px solid rgba(226, 232, 240, 0.8);

      &:hover {
        background: rgba(226, 232, 240, 1);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
    }

    &.btn-primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }

    i {
      font-size: 16px;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

// Card Footer with Actions at Bottom
.modern-ad-card {
  .card-footer {
    padding: 0;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 0 0 20px 20px;

    .footer-divider {
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(226, 232, 240, 0.6), transparent);
      margin: 0 20px;
    }

    .card-actions {
      display: flex;
      gap: 0;
      padding: 16px 20px;

      .action-btn {
        flex: 1;
        height: 40px;
        border: none;
        background: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 13px;
        font-weight: 600;
        border-radius: 10px;
        margin: 0 4px;

        i {
          font-size: 14px;
        }

        span {
          font-weight: 600;
          letter-spacing: 0.3px;
        }

        &.edit-btn {
          color: #f59e0b;
          border: 1px solid rgba(251, 191, 36, 0.2);

          &:hover {
            background: rgba(251, 191, 36, 0.1);
            border-color: rgba(251, 191, 36, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(251, 191, 36, 0.2);
          }
        }

        &.delete-btn {
          color: #ef4444;
          border: 1px solid rgba(239, 68, 68, 0.2);

          &:hover {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
          }
        }
      }
    }
  }
}

// Content Type Selector Styles
.content-type-selector {
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
  border-radius: 20px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);

  .selector-header {
    text-align: center;
    margin-bottom: 25px;

    h3 {
      font-size: 22px;
      font-weight: 700;
      color: #1a202c;
      margin: 0 0 8px 0;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      font-size: 14px;
      color: #64748b;
      margin: 0;
      font-weight: 500;
    }
  }

  .type-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 15px;
    }
  }

  .type-option {
    cursor: pointer;
    display: block;
    position: relative;

    input[type="radio"] {
      display: none;
    }

    .option-card {
      padding: 20px;
      border: 2px solid rgba(226, 232, 240, 0.8);
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.7);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(5px);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.6s;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(102, 126, 234, 0.3);

        &::before {
          left: 100%;
        }
      }
    }

    &.active .option-card {
      border-color: #667eea;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
      transform: translateY(-2px);

      .option-check {
        opacity: 1;
        transform: scale(1);
      }

      .option-icon {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        transform: scale(1.1);
      }
    }

    .option-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      transition: all 0.3s ease;
      font-size: 20px;

      &.ad-icon {
        background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(245, 158, 11, 0.2));
        color: #f59e0b;
      }

      &.event-icon {
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.2));
        color: #16a34a;
      }
    }

    .option-content {
      h4 {
        font-size: 18px;
        font-weight: 700;
        color: #1a202c;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 13px;
        color: #64748b;
        margin: 0;
        line-height: 1.5;
        font-weight: 500;
      }
    }

    .option-check {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}

// Event Date Info Styles
.event-date-info {
  margin-bottom: 20px;

  .info-card {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 12px;
    gap: 15px;

    i {
      font-size: 20px;
      color: #16a34a;
      flex-shrink: 0;
    }

    .info-content {
      h5 {
        font-size: 14px;
        font-weight: 700;
        color: #1a202c;
        margin: 0 0 4px 0;
      }

      p {
        font-size: 12px;
        color: #64748b;
        margin: 0;
        line-height: 1.4;
      }
    }
  }
}

// Loading States
.skeleton-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  animation: pulse 1.5s ease-in-out infinite alternate;

  .skeleton-content {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .skeleton-image {
      width: 100%;
      height: 200px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 12px;
    }

    .skeleton-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;

      .skeleton-button {
        width: 80px;
        height: 36px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 8px;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

// Loading Button States
.modern-btn {
  position: relative;
  transition: all 0.3s ease;

  &.loading {
    pointer-events: none;
    opacity: 0.8;

    nz-spin {
      margin-right: 8px;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Card Loading Animation
.modern-ad-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }
  &:nth-child(6) { animation-delay: 0.6s; }
  &:nth-child(7) { animation-delay: 0.7s; }
  &:nth-child(8) { animation-delay: 0.8s; }
  &:nth-child(9) { animation-delay: 0.9s; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Loading Overlay for Actions
.action-btn {
  position: relative;
  transition: all 0.3s ease;

  &.loading {
    pointer-events: none;
    opacity: 0.7;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    i, span {
      opacity: 0;
    }
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

// Enhanced Loading States
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 16px;

  .loading-content {
    text-align: center;
    color: #666;

    .loading-spinner {
      font-size: 24px;
      margin-bottom: 12px;
    }

    .loading-text {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// Smooth transitions for content
.ads-grid {
  transition: opacity 0.3s ease, transform 0.3s ease;

  &.loading {
    opacity: 0.5;
    transform: scale(0.98);
  }
}

// Enhanced skeleton loading
.skeleton-card {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: skeleton-loading 1.5s infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// Button loading states
.modern-btn {
  &.loading {
    background: linear-gradient(135deg, #d9d9d9, #bfbfbf) !important;
    color: #8c8c8c !important;

    nz-spin {
      ::ng-deep .ant-spin-dot {
        i {
          background-color: #8c8c8c !important;
        }
      }
    }
  }
}

// Close button loading states
.close-btn {
  position: relative;
  transition: all 0.3s ease;

  &.loading {
    pointer-events: none;
    opacity: 0.6;
    background: rgba(255, 255, 255, 0.1) !important;

    nz-spin {
      ::ng-deep .ant-spin-dot {
        i {
          background-color: rgba(255, 255, 255, 0.8) !important;
        }
      }
    }
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
    background: rgba(255, 255, 255, 0.1) !important;

    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
      transform: none !important;
    }
  }
}

// Drawer mask enhancement when not closable
::ng-deep .ant-drawer-mask {
  &.non-closable {
    cursor: not-allowed;
  }
}

// Form loading overlay
.ultra-modern-form {
  position: relative;

  &.loading {
    pointer-events: none;
    opacity: 0.7;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(2px);
      z-index: 10;
      border-radius: 12px;
    }
  }
}

// Header loading indicator
.loading-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;

  nz-spin {
    ::ng-deep .ant-spin-dot {
      i {
        background-color: #1890ff !important;
      }
    }
  }
}

// Drawer header enhancements
.modern-drawer-header {
  .header-text {
    h3 {
      display: flex;
      align-items: center;
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;

      .loading-indicator {
        animation: pulse 1.5s ease-in-out infinite alternate;
      }
    }

    p {
      margin: 4px 0 0 0;
      font-size: 14px;
      color: #666;
      opacity: 0.8;
    }
  }
}

// Loading notice banner
.loading-notice {
  margin: 0 -24px 24px -24px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #e6f7ff, #bae7ff);
  border-bottom: 1px solid #91d5ff;
  animation: slideDown 0.3s ease-out;

  .notice-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;

    i {
      color: #1890ff;
      font-size: 18px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .notice-text {
      flex: 1;

      strong {
        display: block;
        color: #1890ff;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      p {
        margin: 0;
        color: #0050b3;
        font-size: 13px;
        line-height: 1.4;
        opacity: 0.9;
      }
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Enhanced Upload Area Styles
.upload-area {
  .modern-upload {
    ::ng-deep .ant-upload-select {
      border: 2px dashed #d9d9d9;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        background: rgba(24, 144, 255, 0.02);
      }
    }

    ::ng-deep .ant-upload-list-picture-card {
      .ant-upload-list-item {
        border-radius: 12px;
        overflow: hidden;
        border: 2px solid #f0f0f0;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }
      }
    }
  }

  .upload-content {
    padding: 32px 16px;
    text-align: center;
    color: #666;

    .upload-icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;
      opacity: 0.8;
    }

    .upload-text {
      .upload-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
      }

      .upload-subtitle {
        font-size: 14px;
        color: #999;
        margin: 0;
      }
    }
  }

  .upload-limit-reached {
    padding: 32px 16px;
    text-align: center;
    background: rgba(82, 196, 26, 0.05);
    border-radius: 12px;

    .limit-message {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: #52c41a;
      font-weight: 500;

      i {
        font-size: 18px;
      }
    }
  }
}

// Upload Info Section
.upload-info {
  margin-top: 16px;
  padding: 16px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid #1890ff;

  .info-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
    color: #1890ff;
    font-size: 14px;

    i {
      font-size: 16px;
    }
  }

  .info-items {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;

    i {
      color: #52c41a;
      font-size: 14px;
      width: 16px;
      flex-shrink: 0;
    }
  }
}

// File Status Section
.file-status {
  margin-top: 16px;
  padding: 16px;
  background: rgba(82, 196, 26, 0.05);
  border-radius: 8px;
  border-left: 4px solid #52c41a;

  .status-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
    color: #52c41a;
    font-size: 14px;

    i {
      font-size: 16px;
    }
  }

  .file-details {
    background: white;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid rgba(82, 196, 26, 0.2);

    .file-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .file-name {
        font-weight: 500;
        color: #333;
        font-size: 14px;
        flex: 1;
        margin-right: 12px;
        word-break: break-all;
      }

      .file-size {
        color: #666;
        font-size: 12px;
        background: rgba(82, 196, 26, 0.1);
        padding: 2px 8px;
        border-radius: 4px;
        white-space: nowrap;
      }
    }

    .file-actions {
      display: flex;
      gap: 8px;

      button {
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;

        i {
          font-size: 14px;
        }

        &.preview-btn {
          background: rgba(24, 144, 255, 0.1);
          color: #1890ff;

          &:hover {
            background: rgba(24, 144, 255, 0.2);
          }
        }

        &.remove-btn {
          background: rgba(255, 77, 79, 0.1);
          color: #ff4d4f;

          &:hover {
            background: rgba(255, 77, 79, 0.2);
          }
        }
      }
    }
  }
}

// File validation error states
::ng-deep .ant-upload-list-item-error {
  border-color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.05) !important;
}

// Success state for uploaded files
::ng-deep .ant-upload-list-item-done {
  border-color: #52c41a !important;
  background: rgba(82, 196, 26, 0.05) !important;
}

// Debug info styling
.debug-info {
  margin-top: 10px;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

// Selected file info
.selected-file-info {
  margin-top: 16px;
  padding: 12px 16px;
  background: rgba(82, 196, 26, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(82, 196, 26, 0.2);

  .info-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #52c41a;
    font-size: 14px;
    font-weight: 600;

    i {
      font-size: 16px;
    }
  }

  .file-summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid rgba(82, 196, 26, 0.1);

    .file-details {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .file-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .file-size {
        font-size: 12px;
        color: #666;
      }
    }

    .remove-file-btn {
      background: none;
      border: none;
      color: #ff4d4f;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 77, 79, 0.1);
        color: #ff7875;
      }

      i {
        font-size: 16px;
      }
    }
  }
}
