<app-validate-permission [pageId]="'event-ads-report-control'" (permissionChecked)="isPageAllowed = $event"></app-validate-permission>
<div *ngIf="isPageAllowed" class="reports-container">
  <!-- Header Section -->
  <div class="reports-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">
          <i class="feather icon-bar-chart-2"></i>
          Events & Ads Report
        </h1>
        <p class="page-subtitle">View comprehensive reports of your events and advertisements</p>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section">
    <nz-card nzTitle="Filter Reports" class="filter-card">
      <div class="filter-content">
        <div class="date-filters">
          <div class="date-filter-group">
            <label class="filter-label">
              <i class="feather icon-calendar"></i>
              Start Date (Ethiopian Calendar)
            </label>
            <app-ethiopian-datepicker
              [(ngModel)]="startDate"
              (dateChange)="onStartDateChange($event)"
              label=""
              [preventPastDates]="false">
            </app-ethiopian-datepicker>
          </div>

          <div class="date-filter-group">
            <label class="filter-label">
              <i class="feather icon-calendar"></i>
              End Date (Ethiopian Calendar)
            </label>
            <app-ethiopian-datepicker
              [(ngModel)]="endDate"
              (dateChange)="onEndDateChange($event)"
              label=""
              [preventPastDates]="false">
            </app-ethiopian-datepicker>
          </div>
        </div>

        <div class="filter-actions">
          <button nz-button nzType="primary" (click)="loadReports()" [nzLoading]="loading" class="apply-btn">
            <i class="feather icon-search"></i>
            Apply Filters
          </button>
          <button nz-button nzType="default" (click)="clearFilters()" class="clear-btn">
            <i class="feather icon-x"></i>
            Clear Filters
          </button>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Results Section -->
  <div class="results-section">
    <div class="results-header">
      <h2 class="results-title">
        <i class="feather icon-list"></i>
        Report Results
        <span class="results-count" *ngIf="!loading">({{ reportItems.length }} items)</span>
      </h2>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="loading-container">
      <nz-spin nzSize="large">
        <div class="loading-content">
          <p>Loading reports...</p>
        </div>
      </nz-spin>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && reportItems.length === 0" class="empty-container">
      <nz-empty
        nzNotFoundImage="simple"
        nzNotFoundContent="No reports found for the selected criteria">
        <button nz-button nzType="primary" (click)="clearFilters()">
          <i class="feather icon-refresh-cw"></i>
          Show All Reports
        </button>
      </nz-empty>
    </div>

    <!-- Report Items Grid -->
    <div *ngIf="!loading && reportItems.length > 0" class="reports-grid">
      <div *ngFor="let item of reportItems" class="report-card">
        <nz-card class="item-card" [nzCover]="coverTemplate">
          <ng-template #coverTemplate>
            <div class="card-cover">
              <img
                nz-image
                [nzSrc]="getImageUrl(item.imageUrl)"
                [nzFallback]="'assets/images/placeholder.png'"
                [nzPlaceholder]="'assets/images/placeholder.png'"
                width="100%"
                height="200px"
                [nzDisablePreview]="!item.imageUrl"
                alt="{{ item.title }}"
              />
              <div class="card-overlay">
                <nz-tag [nzColor]="item.type === 'Event' ? 'green' : 'orange'" class="type-tag">
                  <i class="feather" [class.icon-calendar]="item.type === 'Event'" [class.icon-megaphone]="item.type === 'Ad'"></i>
                  {{ item.type }}
                </nz-tag>
              </div>
            </div>
          </ng-template>

          <nz-card-meta
            [nzTitle]="item.title"
            [nzDescription]="truncateDescription(item.description)">
          </nz-card-meta>

          <div class="card-footer">
            <div class="posted-info">
              <i class="feather icon-user"></i>
              <span>{{ item.postedBy }}</span>
            </div>
            <div class="date-info">
              <i class="feather icon-clock"></i>
              <span>{{ formatDate(item.createdAt) }}</span>
            </div>
          </div>
        </nz-card>
      </div>
    </div>
  </div>
</div>
