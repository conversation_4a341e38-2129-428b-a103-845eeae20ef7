import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageModule, NzMessageService } from 'ng-zorro-antd/message';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzTagModule } from 'ng-zorro-antd/tag';
import ValidatePermissionComponent from 'src/app/theme/shared/components/validate-permission/validate-permission.component';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import { EthiopianDatepickerComponent } from '../../../../shared/components/ethiopian-datepicker/ethiopian-datepicker.component';
import { ReportsService, ReportItem } from '../reports.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-event-ads-report',
  imports: [
    SharedModule,
    CommonModule,
    FormsModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzCardModule,
    NzImageModule,
    NzSpinModule,
    NzMessageModule,
    NzEmptyModule,
    NzTagModule,
    ValidatePermissionComponent,
    EthiopianDatepickerComponent,
     
  ],
  templateUrl: './event-ads-report.component.html',
  styleUrl: './event-ads-report.component.scss',
  providers: [ReportsService]
})
export default class EventAdsReportComponent implements OnInit {
  isPageAllowed: any;
  reportItems: ReportItem[] = [];
  loading = false;
  startDate: any = null;
  endDate: any = null;
  public env = environment;

  constructor(
    private reportsService: ReportsService,
    private message: NzMessageService
  ) {}

  ngOnInit() {
    this.loadReports();
  }

  onStartDateChange(date: any) {
    this.startDate = date;
  }

  onEndDateChange(date: any) {
    this.endDate = date;
  }

  loadReports() {
    this.loading = true;

    let startDateStr = '';
    let endDateStr = '';

    if (this.startDate) {
      startDateStr = this.reportsService.formatEthiopianDateForApi(this.startDate);
    }

    if (this.endDate) {
      endDateStr = this.reportsService.formatEthiopianDateForApi(this.endDate);
    }

    this.reportsService.getPostingReports(startDateStr, endDateStr).subscribe({
      next: (data) => {
        this.reportItems = data;
        this.loading = false;
        this.message.success(`Loaded ${data.length} report items`);
      },
      error: (error) => {
        console.error('Error loading reports:', error);
        this.loading = false;
        this.message.error('Failed to load reports');
      }
    });
  }

  clearFilters() {
    this.startDate = null;
    this.endDate = null;
    this.loadReports();
  }

  getImageUrl(imageUrl: string | null): string {
    if (!imageUrl) {
      return 'assets/images/placeholder.png';
    }
    return `${this.env.URL}/${imageUrl}`;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  truncateDescription(description: string, limit: number = 150): string {
    if (description.length <= limit) {
      return description;
    }
    return description.substring(0, limit) + '...';
  }
}
