import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import ValidatePermissionComponent from 'src/app/theme/shared/components/validate-permission/validate-permission.component';
import { SharedModule } from 'src/app/theme/shared/shared.module';

@Component({
  selector: 'app-event-ads-report',
  imports: [SharedModule,FormsModule, // For ngModel
      NzInputModule, // For nz-input-group
      NzButtonModule, // For nz-button
      NzIconModule,
      ValidatePermissionComponent],
  templateUrl: './event-ads-report.component.html',
  styleUrl: './event-ads-report.component.scss'
})
export default class EventAdsReportComponent {
 isPageAllowed:any;
}
