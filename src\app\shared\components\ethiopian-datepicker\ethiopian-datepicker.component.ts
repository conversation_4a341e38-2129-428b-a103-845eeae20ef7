import { Component, Input, Output, EventEmitter, forwardRef, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, ValidationErrors, Validator, NG_VALIDATORS, AbstractControl } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-ethiopian-datepicker',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="ethiopian-datepicker">
      <label *ngIf="label" class="form-label">{{ label }}</label>

      <div class="row">
        <!-- Year Dropdown -->
        <div class="col-md-4">
          <select
            class="form-control"
            [(ngModel)]="selectedYear"
            (change)="onDateChange()"
            [disabled]="disabled"
          >
            <option value="">Year</option>
            <option *ngFor="let year of years" [value]="year">{{ year }}</option>
          </select>
        </div>

        <!-- Month Dropdown -->
        <div class="col-md-4">
          <select
            class="form-control"
            [(ngModel)]="selectedMonth"
            (change)="onDateChange()"
            [disabled]="disabled"
          >
            <option value="">Month</option>
            <option *ngFor="let month of ethiopianMonths" [value]="month.value">
              {{ month.name }} ({{ month.nameEn }})
            </option>
          </select>
        </div>

        <!-- Day Dropdown -->
        <div class="col-md-4">
          <select
            class="form-control"
            [(ngModel)]="selectedDay"
            (change)="onDateChange()"
            [disabled]="disabled"
          >
            <option value="">Day</option>
            <option *ngFor="let day of availableDays" [value]="day">{{ day }}</option>
          </select>
        </div>
      </div>

      <!-- Display selected date -->
      <div *ngIf="displayValue" class="mt-2">
        <small class="text-muted">
          Selected: <strong>{{ displayValue }}</strong>
          <span *ngIf="gregorianDisplayValue" class="ml-2">
            (Gregorian: {{ gregorianDisplayValue }})
          </span>
        </small>
      </div>

      <div *ngIf="showError" class="text-danger mt-1">
        <small>{{ errorMessage }}</small>
      </div>
    </div>
  `,
  styleUrls: ['./ethiopian-datepicker.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => EthiopianDatepickerComponent),
      multi: true
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => EthiopianDatepickerComponent),
      multi: true
    }
  ]
})
export class EthiopianDatepickerComponent implements OnInit, ControlValueAccessor, Validator {
  @Input() label: string = '';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() startYear: number = 2010; // Ethiopian year
  @Input() endYear: number = 2040; // Ethiopian year
  @Input() minDate: any = null; // Minimum allowed date
  @Input() maxDate: any = null; // Maximum allowed date
  @Input() preventPastDates: boolean = true; // Prevent selecting past dates

  @Output() dateChange = new EventEmitter<any>();

  selectedYear: number | null = null;
  selectedMonth: number | null = null;
  selectedDay: number | null = null;

  displayValue: string = '';
  gregorianDisplayValue: string = '';
  showError: boolean = false;
  errorMessage: string = '';

  years: number[] = [];
  availableDays: number[] = [];
  currentEthiopianDate: any = null; // Current date in Ethiopian calendar

  private onChange = (value: any) => {};
  private onTouched = () => {};

  // Ethiopian calendar data
  ethiopianMonths = [
    { value: 1, name: 'መስከረም', nameEn: 'Meskerem', days: 30 },
    { value: 2, name: 'ጥቅምት', nameEn: 'Tikimt', days: 30 },
    { value: 3, name: 'ኅዳር', nameEn: 'Hidar', days: 30 },
    { value: 4, name: 'ታኅሣሥ', nameEn: 'Tahsas', days: 30 },
    { value: 5, name: 'ጥር', nameEn: 'Tir', days: 30 },
    { value: 6, name: 'የካቲት', nameEn: 'Yekatit', days: 30 },
    { value: 7, name: 'መጋቢት', nameEn: 'Megabit', days: 30 },
    { value: 8, name: 'ሚያዝያ', nameEn: 'Miazia', days: 30 },
    { value: 9, name: 'ግንቦት', nameEn: 'Ginbot', days: 30 },
    { value: 10, name: 'ሰኔ', nameEn: 'Sene', days: 30 },
    { value: 11, name: 'ሐምሌ', nameEn: 'Hamle', days: 30 },
    { value: 12, name: 'ነሐሴ', nameEn: 'Nehase', days: 30 },
    { value: 13, name: 'ጳጉሜን', nameEn: 'Pagumen', days: 5 } // 5 days normally, 6 in leap years
  ];

  ngOnInit() {
    console.log('🇪🇹 Ethiopian DatePicker initialized');
    this.generateYears();
    this.initializeDefaultDays();
    this.getCurrentEthiopianDate();
  }

  private generateYears() {
    this.years = [];
    for (let year = this.startYear; year <= this.endYear; year++) {
      this.years.push(year);
    }
  }

  private initializeDefaultDays() {
    // Initialize with 30 days by default (most Ethiopian months have 30 days)
    this.availableDays = [];
    for (let day = 1; day <= 30; day++) {
      this.availableDays.push(day);
    }
  }

  private updateAvailableDays() {
    this.availableDays = [];

    if (this.selectedMonth) {
      const selectedMonthData = this.ethiopianMonths.find(m => m.value === this.selectedMonth);
      if (selectedMonthData) {
        let maxDays = selectedMonthData.days;

        // Handle Pagumen (13th month) leap year logic
        if (this.selectedMonth === 13 && this.selectedYear) {
          maxDays = this.isEthiopianLeapYear(this.selectedYear) ? 6 : 5;
        } else if (this.selectedMonth === 13) {
          // Default to 5 days for Pagumen if no year selected
          maxDays = 5;
        }

        for (let day = 1; day <= maxDays; day++) {
          this.availableDays.push(day);
        }
      }
    } else {
      // If no month selected, show 30 days by default
      for (let day = 1; day <= 30; day++) {
        this.availableDays.push(day);
      }
    }

    // Reset day if it's no longer valid
    if (this.selectedDay && this.availableDays.indexOf(this.selectedDay) === -1) {
      this.selectedDay = null;
    }
  }

  private isEthiopianLeapYear(year: number): boolean {
    // Ethiopian leap year calculation
    return (year % 4) === 3;
  }

  private getCurrentEthiopianDate(): void {
    // Convert current Gregorian date to Ethiopian
    const today = new Date();
    this.currentEthiopianDate = this.convertGregorianToEthiopian(today);
  }

  private convertGregorianToEthiopian(gregorianDate: Date): any {
    try {
      // This is a simplified conversion - for production use a proper library
      // Ethiopian calendar is approximately 7-8 years behind Gregorian
      const year = gregorianDate.getFullYear();
      const month = gregorianDate.getMonth() + 1; // JavaScript months are 0-indexed
      const day = gregorianDate.getDate();

      // Simplified conversion logic
      // Ethiopian New Year starts around September 11 (Gregorian)
      let ethYear = year - 7;
      let ethMonth = 1;
      let ethDay = 1;

      // Calculate days since Ethiopian New Year
      const ethiopianNewYear = new Date(year, 8, 11); // September 11
      if (gregorianDate < ethiopianNewYear) {
        ethYear = year - 8;
        ethiopianNewYear.setFullYear(year - 1);
      }

      const daysDiff = Math.floor((gregorianDate.getTime() - ethiopianNewYear.getTime()) / (1000 * 60 * 60 * 24));

      // Calculate Ethiopian month and day
      ethMonth = Math.floor(daysDiff / 30) + 1;
      ethDay = (daysDiff % 30) + 1;

      // Handle month overflow
      if (ethMonth > 13) {
        ethYear++;
        ethMonth = 1;
      }

      // Handle 13th month (Pagumen) with only 5-6 days
      if (ethMonth === 13 && ethDay > (this.isEthiopianLeapYear(ethYear) ? 6 : 5)) {
        ethYear++;
        ethMonth = 1;
        ethDay = 1;
      }

      return {
        year: ethYear,
        month: ethMonth,
        day: ethDay
      };
    } catch (error) {
      console.error('Error converting Gregorian to Ethiopian date:', error);
      return null;
    }
  }

  onDateChange() {
    // Convert string values to numbers
    if (this.selectedYear) this.selectedYear = Number(this.selectedYear);
    if (this.selectedMonth) this.selectedMonth = Number(this.selectedMonth);
    if (this.selectedDay) this.selectedDay = Number(this.selectedDay);

    this.updateAvailableDays();

    if (this.selectedYear && this.selectedMonth && this.selectedDay) {
      // Validate the selected date
      const validationResult = this.validateSelectedDate();
      if (validationResult.isValid) {
        this.showError = false;
        this.errorMessage = '';
        this.updateDisplayValues();
        this.emitDateChange();
      } else {
        this.showError = true;
        this.errorMessage = validationResult.errorMessage;
        this.displayValue = '';
        this.gregorianDisplayValue = '';
        this.onChange(null);
        this.dateChange.emit(null);
      }
    } else {
      this.showError = false;
      this.errorMessage = '';
      this.displayValue = '';
      this.gregorianDisplayValue = '';
      this.onChange(null);
      this.dateChange.emit(null);
    }

    this.onTouched();
  }

  private validateSelectedDate(): { isValid: boolean; errorMessage: string } {
    if (!this.selectedYear || !this.selectedMonth || !this.selectedDay) {
      return { isValid: true, errorMessage: '' };
    }

    const selectedDate = {
      year: this.selectedYear,
      month: this.selectedMonth,
      day: this.selectedDay
    };

    // Check if date is in the past (if preventPastDates is enabled)
    if (this.preventPastDates && this.currentEthiopianDate) {
      if (this.isDateBefore(selectedDate, this.currentEthiopianDate)) {
        return {
          isValid: false,
          errorMessage: 'Cannot select a date in the past'
        };
      }
    }

    // Check against minDate if provided
    if (this.minDate && this.minDate.ethiopian) {
      if (this.isDateBefore(selectedDate, this.minDate.ethiopian)) {
        return {
          isValid: false,
          errorMessage: 'Date is before the minimum allowed date'
        };
      }
    }

    // Check against maxDate if provided
    if (this.maxDate && this.maxDate.ethiopian) {
      if (this.isDateAfter(selectedDate, this.maxDate.ethiopian)) {
        return {
          isValid: false,
          errorMessage: 'Date is after the maximum allowed date'
        };
      }
    }

    return { isValid: true, errorMessage: '' };
  }

  private isDateBefore(date1: any, date2: any): boolean {
    if (date1.year < date2.year) return true;
    if (date1.year > date2.year) return false;
    if (date1.month < date2.month) return true;
    if (date1.month > date2.month) return false;
    return date1.day < date2.day;
  }

  private isDateAfter(date1: any, date2: any): boolean {
    if (date1.year > date2.year) return true;
    if (date1.year < date2.year) return false;
    if (date1.month > date2.month) return true;
    if (date1.month < date2.month) return false;
    return date1.day > date2.day;
  }

  private isDateEqual(date1: any, date2: any): boolean {
    return date1.year === date2.year &&
           date1.month === date2.month &&
           date1.day === date2.day;
  }

  private updateDisplayValues() {
    if (this.selectedYear && this.selectedMonth && this.selectedDay) {
      const monthData = this.ethiopianMonths.find(m => m.value === this.selectedMonth);
      if (monthData) {
        this.displayValue = `${this.selectedDay} ${monthData.name} ${this.selectedYear}`;

        // Convert to Gregorian
        const gregorianDate = this.convertEthiopianToGregorian(
          this.selectedYear,
          this.selectedMonth,
          this.selectedDay
        );

        if (gregorianDate) {
          this.gregorianDisplayValue = gregorianDate.toLocaleDateString();
        }
      }
    }
  }

  private emitDateChange() {
    if (this.selectedYear && this.selectedMonth && this.selectedDay) {
      const monthData = this.ethiopianMonths.find(m => m.value === this.selectedMonth);
      const gregorianDate = this.convertEthiopianToGregorian(
        this.selectedYear,
        this.selectedMonth,
        this.selectedDay
      );

      const dateValue = {
        ethiopian: {
          year: this.selectedYear,
          month: this.selectedMonth,
          day: this.selectedDay,
          monthName: monthData?.name,
          monthNameEn: monthData?.nameEn
        },
        gregorian: gregorianDate,
        iso: gregorianDate ? gregorianDate.toISOString().split('T')[0] : null
      };

      this.onChange(dateValue);
      this.dateChange.emit(dateValue);
    }
  }

  private convertEthiopianToGregorian(ethYear: number, ethMonth: number, ethDay: number): Date | null {
    try {
      // Ethiopian calendar starts on September 11 (or 12 in leap years) in Gregorian calendar
      // This is a simplified conversion - for production use a proper library

      const ethiopianNewYear = new Date(ethYear + 7, 8, 11); // September 11

      // Calculate days from Ethiopian new year
      let totalDays = 0;

      // Add days for complete months
      for (let month = 1; month < ethMonth; month++) {
        const monthData = this.ethiopianMonths.find(m => m.value === month);
        if (monthData) {
          if (month === 13) {
            totalDays += this.isEthiopianLeapYear(ethYear) ? 6 : 5;
          } else {
            totalDays += monthData.days;
          }
        }
      }

      // Add days in current month
      totalDays += ethDay - 1;

      // Create Gregorian date
      const gregorianDate = new Date(ethiopianNewYear);
      gregorianDate.setDate(gregorianDate.getDate() + totalDays);

      return gregorianDate;

    } catch (error) {
      console.error('Error converting Ethiopian to Gregorian date:', error);
      return null;
    }
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    if (value) {
      if (value.ethiopian) {
        this.selectedYear = value.ethiopian.year;
        this.selectedMonth = value.ethiopian.month;
        this.selectedDay = value.ethiopian.day;
        this.updateAvailableDays();
        this.updateDisplayValues();
      }
    } else {
      this.selectedYear = null;
      this.selectedMonth = null;
      this.selectedDay = null;
      this.displayValue = '';
      this.gregorianDisplayValue = '';
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Validator interface implementation
  validate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const value = control.value;
    if (!value.ethiopian) {
      return null;
    }

    const selectedDate = value.ethiopian;

    // Check if date is in the past (if preventPastDates is enabled)
    if (this.preventPastDates && this.currentEthiopianDate) {
      if (this.isDateBefore(selectedDate, this.currentEthiopianDate)) {
        return { pastDate: { message: 'Cannot select a date in the past' } };
      }
    }

    // Check against minDate if provided
    if (this.minDate && this.minDate.ethiopian) {
      if (this.isDateBefore(selectedDate, this.minDate.ethiopian)) {
        return { minDate: { message: 'Date is before the minimum allowed date' } };
      }
    }

    // Check against maxDate if provided
    if (this.maxDate && this.maxDate.ethiopian) {
      if (this.isDateAfter(selectedDate, this.maxDate.ethiopian)) {
        return { maxDate: { message: 'Date is after the maximum allowed date' } };
      }
    }

    return null;
  }
}
