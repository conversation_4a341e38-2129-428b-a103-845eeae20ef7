import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface ReportItem {
  postedBy: string;
  type: 'Ad' | 'Event';
  title: string;
  description: string;
  imageUrl: string | null;
  createdAt: string;
}

export interface ReportsResponse {
  success: boolean;
  message: string;
  data: ReportItem[];
}

@Injectable({
  providedIn: 'root'
})
export class ReportsService {
  private reportsApiUrl = environment.URL + '/reports';

  constructor(private http: HttpClient) { }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders().set('Authorization', `Bearer ${token}`);
  }

  /**
   * Get posting reports with optional date filters
   * @param startDate Optional start date in Ethiopian format (YYYY,MM,DD)
   * @param endDate Optional end date in Ethiopian format (YYYY,MM,DD)
   * @returns Observable of report items
   */
  getPostingReports(startDate?: string, endDate?: string): Observable<ReportItem[]> {
    let url = `${this.reportsApiUrl}/posting`;
    
    const params: string[] = [];
    if (startDate) {
      params.push(`startDate=${startDate}`);
    }
    if (endDate) {
      params.push(`endDate=${endDate}`);
    }
    
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    return this.http.get<ReportItem[]>(url, { headers: this.getHeaders() });
  }

  /**
   * Convert Ethiopian date to API format (YYYY,MM,DD)
   * @param ethiopianDate Ethiopian date object
   * @returns Formatted date string
   */
  formatEthiopianDateForApi(ethiopianDate: any): string {
    if (!ethiopianDate || !ethiopianDate.ethiopian) {
      return '';
    }
    
    const { year, month, day } = ethiopianDate.ethiopian;
    return `${year},${month},${day}`;
  }
}
