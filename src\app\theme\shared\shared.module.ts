// angular import
import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// project import
import { CardComponent } from './components/card/card.component';

// bootstrap import
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgbCollapseModule } from '@ng-bootstrap/ng-bootstrap';
import { NzButtonModule } from 'ng-zorro-antd/button';
// third party
import { NgScrollbarModule } from 'ngx-scrollbar';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import {MatInputModule} from '@angular/material/input';
import {MatSelectModule} from '@angular/material/select';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatCheckboxModule} from '@angular/material/checkbox';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import {MatRadioModule} from '@angular/material/radio';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzImageModule } from 'ng-zorro-antd/image';

@NgModule({
  declarations: [],
  imports: [CommonModule, FormsModule,NzEmptyModule, ReactiveFormsModule, CardComponent,
     NgbModule, NgScrollbarModule, NgbCollapseModule,NzButtonModule,NzDrawerModule,NzAlertModule,
     NzDividerModule,MatRadioModule,NzResultModule,
     MatInputModule,MatSelectModule,MatFormFieldModule,
     MatCheckboxModule, NzDatePickerModule,NzUploadModule,NzSpinModule,NzTimePickerModule,
     NzPopconfirmModule,NzModalModule,NzPaginationModule,NzTableModule,NzBadgeModule,NzIconModule,NzSelectModule, NzImageModule],
  exports: [CommonModule, FormsModule,NzEmptyModule, ReactiveFormsModule,
     CardComponent, NgbModule, NgScrollbarModule,
      NgbCollapseModule,NzButtonModule,NzDrawerModule,
      MatInputModule,MatSelectModule,MatFormFieldModule,MatRadioModule,NzResultModule,
      NzDividerModule,
      MatCheckboxModule,
      NzDatePickerModule,
      NzUploadModule,NzSpinModule,NzTimePickerModule,NzPopconfirmModule,NzModalModule,
      NzPaginationModule,NzTableModule,NzBadgeModule,NzIconModule,NzSelectModule,NzAlertModule, NzImageModule
    ],
    providers: [
      DatePipe // Provide DatePipe here so it's available via SharedModule
    ]
})
export class SharedModule {}
