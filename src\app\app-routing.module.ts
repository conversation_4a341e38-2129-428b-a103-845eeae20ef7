import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AdminComponent } from './theme/layout/admin/admin.component';
import { GuestComponent } from './theme/layout/guest/guest.component';
import AuthSigninComponent from './demo/pages/authentication/auth-signin/auth-signin.component';
import { canActivate } from './demo/pages/authentication/auth-guard.service';

const routes: Routes = [
  {
    path: '',
    component: AuthSigninComponent,
    children: [
      {
        path: '',
        redirectTo: 'auth',
        pathMatch: 'full',
        loadChildren: () => import('./demo/pages/authentication/authentication.module').then((m) => m.AuthenticationModule)
      }
    ]
  },
  {
    path: '',
    component: AdminComponent,
    children: [
      {
        path: 'dashboard',
        loadComponent: () => import('./demo/dashboard/dashboard.component').then((c) => c.DashboardComponent),
        canActivate: [canActivate]
      },
      {
        path: 'screen-control',
        loadComponent: () => import('./demo/pages/screen-control/screen-control.component'),
        canActivate: [canActivate]
      },
      {
        path: 'settings',
        loadChildren: () => import('./demo/pages/settings/settings.module').then((m) => m.SettingsModule),
        canActivate: [canActivate]
      },
      {
        path: 'devices',
        loadComponent: () => import('./demo/pages/devices/devices.component'),
        canActivate: [canActivate]
      },
      {
        path: 'ads_events',
        loadComponent: () => import('./demo/pages/ads/ads.component'),
        canActivate: [canActivate]
      },
      {
        path: 'test-ethiopian',
        loadComponent: () => import('./test-ethiopian-calendar.component').then(c => c.TestEthiopianCalendarComponent),
        canActivate: [canActivate]
      },
      {
        path: 'ads_events_report',
        loadComponent: () => import('./demo/pages/reports/event-ads-report/event-ads-report.component'),
        canActivate: [canActivate]
      },




    ]
  },
  {
    path: '',
    component: GuestComponent,
    children: [
      {
        path: 'auth',
        loadChildren: () => import('./demo/pages/authentication/authentication.module').then((m) => m.AuthenticationModule)
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {}
