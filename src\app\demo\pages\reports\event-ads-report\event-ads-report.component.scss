.reports-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;

  // Header Section
  .reports-header {
    margin-bottom: 24px;

    .header-content {
      .title-section {
        .page-title {
          font-size: 28px;
          font-weight: 700;
          color: #1a202c;
          margin: 0 0 8px 0;
          display: flex;
          align-items: center;
          gap: 12px;

          i {
            font-size: 24px;
            color: #3b82f6;
          }
        }

        .page-subtitle {
          font-size: 16px;
          color: #64748b;
          margin: 0;
          font-weight: 500;
        }
      }
    }
  }

  // Filters Section
  .filters-section {
    margin-bottom: 24px;

    .filter-card {
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;

      ::ng-deep .ant-card-head {
        border-bottom: 1px solid #e2e8f0;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

        .ant-card-head-title {
          font-weight: 600;
          color: #1a202c;
          font-size: 16px;
        }
      }

      .filter-content {
        .date-filters {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 24px;
          margin-bottom: 24px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .date-filter-group {
            .filter-label {
              display: flex;
              align-items: center;
              gap: 8px;
              font-weight: 600;
              color: #374151;
              margin-bottom: 12px;
              font-size: 14px;

              i {
                font-size: 16px;
                color: #6366f1;
              }
            }
          }
        }

        .filter-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;

          @media (max-width: 768px) {
            justify-content: stretch;

            button {
              flex: 1;
            }
          }

          .apply-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            border-radius: 8px;
            height: 40px;
            padding: 0 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            }

            i {
              font-size: 16px;
            }
          }

          .clear-btn {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            height: 40px;
            padding: 0 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #9ca3af;
              background: #f9fafb;
            }

            i {
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  // Results Section
  .results-section {
    .results-header {
      margin-bottom: 20px;

      .results-title {
        font-size: 20px;
        font-weight: 600;
        color: #1a202c;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;

        i {
          font-size: 18px;
          color: #6366f1;
        }

        .results-count {
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
          margin-left: 8px;
        }
      }
    }

    // Loading State
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

      .loading-content {
        text-align: center;

        p {
          margin-top: 16px;
          color: #64748b;
          font-size: 16px;
        }
      }
    }

    // Empty State
    .empty-container {
      background: white;
      border-radius: 12px;
      padding: 48px 24px;
      text-align: center;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

      ::ng-deep .ant-empty {
        .ant-empty-description {
          color: #64748b;
          font-size: 16px;
        }
      }

      button {
        margin-top: 16px;
        border-radius: 8px;
        height: 40px;
        padding: 0 20px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }
    }

    // Reports Grid
    .reports-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 24px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .report-card {
        .item-card {
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          border: 1px solid #e2e8f0;
          transition: all 0.3s ease;
          height: 100%;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
          }

          .card-cover {
            position: relative;
            overflow: hidden;

            img[nz-image] {
              width: 100%;
              height: 200px;
              object-fit: cover;
              transition: transform 0.3s ease;
              border-radius: 0;

              &:hover {
                transform: scale(1.05);
              }
            }

            .card-overlay {
              position: absolute;
              top: 12px;
              right: 12px;
              z-index: 2;

              .type-tag {
                border-radius: 20px;
                padding: 4px 12px;
                font-weight: 600;
                font-size: 12px;
                display: flex;
                align-items: center;
                gap: 6px;
                backdrop-filter: blur(10px);
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.2);

                i {
                  font-size: 14px;
                }

                &.ant-tag-green {
                  color: #16a34a;
                  background: rgba(34, 197, 94, 0.1);
                  border-color: rgba(34, 197, 94, 0.2);
                }

                &.ant-tag-orange {
                  color: #ea580c;
                  background: rgba(234, 88, 12, 0.1);
                  border-color: rgba(234, 88, 12, 0.2);
                }
              }
            }
          }

          ::ng-deep .ant-card-body {
            padding: 20px;

            .ant-card-meta {
              .ant-card-meta-title {
                font-size: 18px;
                font-weight: 600;
                color: #1a202c;
                margin-bottom: 8px;
                line-height: 1.4;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              .ant-card-meta-description {
                color: #64748b;
                font-size: 14px;
                line-height: 1.5;
                margin-bottom: 16px;
              }
            }
          }

          .card-footer {
            padding: 0 20px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;

            @media (max-width: 480px) {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;
            }

            .posted-info,
            .date-info {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 12px;
              color: #64748b;
              font-weight: 500;

              i {
                font-size: 14px;
                color: #9ca3af;
              }
            }

            .posted-info {
              i {
                color: #6366f1;
              }
            }

            .date-info {
              i {
                color: #10b981;
              }
            }
          }
        }
      }
    }
  }
}

// Global overrides for Ant Design components
::ng-deep {
  .ant-card-meta-title {
    white-space: normal !important;
  }

  .ant-image-img {
    transition: transform 0.3s ease;
  }

  .ant-tag {
    border-radius: 20px;
  }
}
